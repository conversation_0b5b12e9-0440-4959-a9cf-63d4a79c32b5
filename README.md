# Titan Bot Process

A modern TypeScript application with Redis caching, database connectivity, and comprehensive tooling.

## 🚀 Features

- **Modern TypeScript**: ES2022 target with strict type checking and type-safe enums
- **Modular Architecture**: Clean separation of concerns with organized directory structure
- **Redis Caching**: Built-in Redis cache management with lazy connection
- **Database Support**: MySQL/MySQL2 and Knex.js integration

- **Telegram Notifications**: Rich notification system with HTML formatting and dual bot support
- **Socket.IO Integration**: Real-time communication with automatic reconnection
- **Service Management**: Centralized service orchestration and health monitoring
- **Type Safety**: Comprehensive enum usage for categorical values and better IntelliSense
- **Comprehensive Tooling**: ESLint, Prettier, and development scripts
- **Docker Support**: Docker Compose for local development
- **Graceful Shutdown**: Proper cleanup and notification on application shutdown

## 📁 Project Structure

```
src/
├── index.ts          # Application entry point
├── types/            # Type definitions
├── libs/             # Core libraries and utilities
│   ├── Logger.ts     # Winston logger configuration
│   ├── Utils.ts      # Utility functions
│   └── BusinessException.ts # Custom exception handling
├── caches/           # Cache implementations
│   └── RedisCache.ts # Redis cache manager
├── services/         # Business logic services
│   ├── TelegramNotificationService.ts # Telegram bot notifications
│   ├── SocketHandler.ts # Socket.IO client handler
│   └── ServiceManager.ts # Service orchestration
├── config/           # Configuration files
│   └── services.ts   # Service configuration factory
├── utils/            # Additional utilities
└── examples/         # Usage examples and demos
```

## 🛠️ Development

### Prerequisites

- Node.js 18+
- npm or yarn
- Redis (for caching)
- MySQL (for database)

### Installation

```bash
npm install
```

### Available Scripts

```bash
# Development
npm run dev              # Start development server with hot reload
npm run dev:watch        # Start with file watching

# Building
npm run build            # Build for production
npm run build:watch      # Build with file watching

# Running
npm start               # Run production build

# Code Quality
npm run lint            # Run ESLint
npm run lint:fix        # Fix ESLint issues automatically
npm run format          # Format code with Prettier
npm run format:check    # Check code formatting

# Utilities
npm run clean           # Clean build directory
```

### Environment Setup

Copy `.env.example` to `.env` and configure your environment variables:

```bash
cp .env.example .env
```

#### Required Environment Variables

```bash
# Dual Telegram Bot Configuration (recommended)
TELEGRAM_ERROR_BOT_TOKEN=bot123:error_token
TELEGRAM_ERROR_CHAT_ID=-1001234567890
TELEGRAM_GENERAL_BOT_TOKEN=bot456:general_token
TELEGRAM_GENERAL_CHAT_ID=-1001234567891

# Legacy Single Bot (fallback)
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_DEFAULT_CHAT_ID=your_chat_id_here

# Socket Connection (optional)
SOCKET_URL=ws://localhost:3000

# Redis Cache (optional)
REDIS_URL=redis://localhost:6379

# MySQL Database (optional)
MYSQL_DB_HOST=localhost
MYSQL_DBNAME=your_database
MYSQL_USERNAME=your_username
MYSQL_PASSWORD=your_password
```

### Docker Development

Start the development environment with Docker:

```bash
docker-compose up -d
```

This will start:
- MySQL database on port 3308
- Redis on port 6380

## 🔧 Services

### Telegram Notification Service

Send rich notifications through Telegram with HTML formatting support. **Supports dual bot configuration** - one dedicated for errors, one for general notifications.

```typescript
import { TelegramNotificationService } from '@services/TelegramNotificationService';

// Single bot usage
const telegram = new TelegramNotificationService({
  token: 'YOUR_BOT_TOKEN',
  defaultChatId: 'YOUR_CHAT_ID',
  parseMode: 'HTML',
});

// Send different types of notifications
await telegram.sendSuccessNotification('Task Completed', 'All operations finished successfully');
await telegram.sendWarningNotification('High Memory Usage', 'Memory usage is at 85%');
await telegram.sendErrorNotification('Database Error', 'Connection to database failed');
```

#### Dual Bot Configuration

Configure separate bots for different notification types:

```bash
# Error Bot - dedicated for error notifications
TELEGRAM_ERROR_BOT_TOKEN=bot123:error_token
TELEGRAM_ERROR_CHAT_ID=-1001234567890

# General Bot - for success, warnings, and general messages
TELEGRAM_GENERAL_BOT_TOKEN=bot456:general_token
TELEGRAM_GENERAL_CHAT_ID=-1001234567891
```

### Socket Handler

Real-time communication with automatic reconnection and message queuing.

```typescript
import { SocketHandler } from '@services/SocketHandler';

const socket = new SocketHandler({
  url: 'ws://localhost:3000',
  options: {
    reconnection: true,
    reconnectionAttempts: 5,
  },
});

await socket.connect();

// Send messages
await socket.send('user_action', { action: 'login', userId: 123 });

// Listen for events
socket.subscribe('price_update', (data) => {
  console.log('Price updated:', data);
});
```

### Service Manager (Recommended)

Centralized service management with health monitoring and graceful shutdown. **Supports dual Telegram bot configuration**.

```typescript
import { ServiceManager, NotificationType, TelegramParseMode } from '@services/ServiceManager';

const serviceManager = new ServiceManager({
  telegram: {
    error: {
      token: process.env.TELEGRAM_ERROR_BOT_TOKEN!,
      defaultChatId: process.env.TELEGRAM_ERROR_CHAT_ID,
      parseMode: TelegramParseMode.HTML,
    },
    general: {
      token: process.env.TELEGRAM_GENERAL_BOT_TOKEN!,
      defaultChatId: process.env.TELEGRAM_GENERAL_CHAT_ID,
      parseMode: TelegramParseMode.HTML,
    },
  },
  socket: {
    url: process.env.SOCKET_URL!,
  },
});

await serviceManager.initialize();

// Send notifications using type-safe enums - automatically routed to appropriate bot
await serviceManager.sendNotification(NotificationType.SUCCESS, 'Service Started', 'All systems operational'); // → General bot
await serviceManager.sendNotification(NotificationType.ERROR, 'Database Error', 'Connection failed'); // → Error bot
await serviceManager.sendNotification(NotificationType.WARNING, 'High CPU', 'CPU usage at 90%'); // → General bot
await serviceManager.sendNotification(NotificationType.MESSAGE, 'Custom Message', 'General information'); // → General bot

// Direct access to specific bots
await serviceManager.telegramError?.sendErrorNotification('Critical Error', 'System failure');
await serviceManager.telegramGeneral?.sendSuccessNotification('Task Complete', 'All done');

// Send socket messages
await serviceManager.sendSocketMessage('app_status', { status: 'running' });

// Health check
const health = await serviceManager.healthCheck();
console.log('Service health:', health);
```

#### Type-Safe Enums

The application uses TypeScript enums for better type safety and IntelliSense support:

```typescript
import { NotificationType, TelegramParseMode, SocketTransport } from '@services/index';

// Notification types
NotificationType.SUCCESS   // 'success'
NotificationType.WARNING   // 'warning'
NotificationType.ERROR     // 'error'
NotificationType.MESSAGE   // 'message'

// Telegram parse modes
TelegramParseMode.HTML        // 'HTML'
TelegramParseMode.MARKDOWN    // 'Markdown'
TelegramParseMode.MARKDOWN_V2 // 'MarkdownV2'

// Socket transports
SocketTransport.WEBSOCKET  // 'websocket'
SocketTransport.POLLING    // 'polling'
```

#### Notification Routing Logic

- **Error notifications** → Error bot (fallback to general bot if error bot not configured)
- **Success/Warning/Message notifications** → General bot
- **Automatic fallback** to available bot if preferred bot not configured

## 🏗️ Architecture

### Dependencies

**Production:**
- `axios` - HTTP client
- `dayjs` - Modern date library (replaces moment.js)
- `redis` - Redis client
- `mysql2` - MySQL database driver
- `knex` - SQL query builder
- `winston` - Logging library
- `dotenv` - Environment variable management

**Development:**
- `typescript` - TypeScript compiler
- `eslint` - Code linting
- `prettier` - Code formatting
- `ts-node` - TypeScript execution

### Key Features

- **Type Safety**: Strict TypeScript configuration with comprehensive type checking
- **Modern ES Features**: ES2022 target with latest JavaScript features
- **Path Mapping**: Clean imports using TypeScript path mapping
- **Error Handling**: Custom business exception handling
- **Logging**: Structured logging with Winston
- **Caching**: Redis-based caching with serialization support

## 📝 License

ISC
# zizi-news
