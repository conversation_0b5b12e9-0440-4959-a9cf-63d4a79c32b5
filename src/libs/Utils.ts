import jwt from 'jsonwebtoken';
import { createHmac, randomUUID } from 'crypto';
import { mapKeys, camelCase, snakeCase } from 'lodash';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import Cache from '../caches/RedisCache';
import { spawn } from 'child_process';
import Logger from './Logger';
import BusinessException from './BusinessException';

dayjs.extend(utc);

export type StringNum = string | number;

export const sleep = (ms: number): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const encodeJwt = (message: string | object | Buffer, privateKey: jwt.Secret, options = {}) => {
  return jwt.sign(message, privateKey, options);
};

export const currentTimestamp = (): number => {
  return Date.now() / 1000;
};

export const getCurrentZuluTime = (): string => {
  return dayjs().utc().format('YYYY-MM-DDTHH:mm:ss[Z]');
};

export const encrypt = (method: string, url: string, timestamp: string, body: string, secret: string): string => {
  const message = method + url + timestamp + body;
  return createHmac('sha256', secret).update(message).digest('hex');
};

export const encodeHmac = (str: string, secret: string, algo = 'sha256'): string => {
  return createHmac(algo, secret).update(str).digest('hex');
};

export const timestampToDate = (timestamp: string, format = 'DD/MM/YYYY'): string => {
  return dayjs(timestamp).format(format);
};

export const camelCaseObjectKey = (obj: any): any => {
  return mapKeys(obj, (_value, key) => {
    return camelCase(key);
  });
};

export const snakeCaseObjectKey = (obj: any): any => {
  return mapKeys(obj, (_value, key) => {
    return snakeCase(key);
  });
};

export const generateUUID = (): string => {
  return randomUUID();
};

export const isJsonString = (str: string): boolean => {
  try {
    JSON.parse(str);
  } catch {
    return false;
  }
  return true;
};

export const randomString = (length: number): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let str = '';

  for (let i = 0; i < length; i++) {
    str += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  return str;
};

export const handleErrorException = (title: string, errorInstance: any): void => {
  let payload = {
    errorMessage: errorInstance.stack || errorInstance.msg || errorInstance.message,
  };

  if (errorInstance instanceof BusinessException) {
    payload = {
      ...payload,
      ...errorInstance.additionalData,
    };
  }

  Cache.pubsub('Channel.Notification', 'App\\Events\\ErrorNotification', {
    title,
    payload,
  });

  Logger.error(title, errorInstance);
};

export const sortObjectKey = (obj: any): any => {
  return Object.keys(obj)
    .sort()
    .reduce(function (result: any, key) {
      result[key] = obj[key];
      return result;
    }, {});
};
