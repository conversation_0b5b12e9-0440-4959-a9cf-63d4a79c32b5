/**
 * Custom business exception class for handling application-specific errors
 */
export default class BusinessException extends Error {
  public additionalData: Record<string, any>;

  constructor(message: string, additionalData: Record<string, any> = {}) {
    super(message);
    this.name = 'BusinessException';
    this.additionalData = additionalData;

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, BusinessException);
    }
  }

  /**
   * Get the additional data associated with this exception
   */
  getAdditionalData(): Record<string, any> {
    return this.additionalData;
  }

  /**
   * Set additional data for this exception
   */
  setAdditionalData(data: Record<string, any>): void {
    this.additionalData = { ...this.additionalData, ...data };
  }

  /**
   * Convert the exception to a JSON object
   */
  toJSON(): Record<string, any> {
    return {
      name: this.name,
      message: this.message,
      stack: this.stack,
      additionalData: this.additionalData,
    };
  }
}
