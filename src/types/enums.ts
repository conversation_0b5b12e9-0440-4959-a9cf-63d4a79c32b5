/**
 * Enums for categorical values used throughout the application
 */

/**
 * Notification types for Telegram messages
 */
export enum NotificationType {
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  MESSAGE = 'message',
}

/**
 * Telegram parse modes
 */
export enum TelegramParseMode {
  HTML = 'HTML',
  MARKDOWN = 'Markdown',
  MARKDOWN_V2 = 'MarkdownV2',
}

export enum SocketTransport {
  WEBSOCKET = 'websocket',
  POLLING = 'polling',
}

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
}

export enum ServiceStatus {
  NOT_INITIALIZED = 'not_initialized',
  INITIALIZING = 'initializing',
  RUNNING = 'running',
  ERROR = 'error',
  SHUTTING_DOWN = 'shutting_down',
  STOPPED = 'stopped',
}

export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}
