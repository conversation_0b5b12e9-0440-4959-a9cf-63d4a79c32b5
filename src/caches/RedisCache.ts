import * as redis from 'redis';
import type { RedisClientType } from 'redis';
import { isEmpty, isNumber } from 'lodash';

class Cache {
  private connection: RedisClientType | null = null;
  private isConnected = false;
  private connectionPromise: Promise<void> | null = null;

  constructor() {
    // Don't initialize connection in constructor
  }

  private async ensureConnection(): Promise<void> {
    if (this.isConnected && this.connection) {
      return;
    }

    if (this.connectionPromise) {
      return this.connectionPromise;
    }

    this.connectionPromise = this.setConnection();
    return this.connectionPromise;
  }

  private async setConnection(): Promise<void> {
    if (!process.env.REDIS_URL) {
      throw new Error('REDIS_URL environment variable is not set');
    }

    try {
      this.connection = redis.createClient({
        url: process.env.REDIS_URL,
      });

      this.connection.on('error', function (err: any) {
        console.error('Redis connection error:', err);
      });

      await this.connection.connect();
      this.isConnected = true;
      this.connectionPromise = null;
    } catch (error) {
      this.connectionPromise = null;
      throw new Error(`Failed to connect to Redis: ${error}`);
    }
  }

  async get(key: string, defaultValue: any): Promise<any> {
    try {
      await this.ensureConnection();
      const value = await this.connection!.get(key);

      if (!value || isEmpty(value)) {
        return defaultValue;
      }

      return this._unserialize(value);
    } catch (error) {
      console.warn('Redis get failed, returning default value:', error);
      return defaultValue;
    }
  }

  async put(key: string, value: any, ttlInMinutes?: number): Promise<void> {
    try {
      await this.ensureConnection();
      const serializedValue = this._serialize(value);

      if (ttlInMinutes && isNumber(ttlInMinutes)) {
        await this.connection!.setEx(key, ttlInMinutes * 60, serializedValue);
      } else {
        await this.connection!.set(key, serializedValue);
      }
    } catch (error) {
      console.warn('Redis put failed:', error);
      throw error;
    }
  }

  async forget(key: string): Promise<void> {
    try {
      await this.ensureConnection();
      await this.connection!.del(key);
    } catch (error) {
      console.warn('Redis forget failed:', error);
      throw error;
    }
  }

  async flush(): Promise<void> {
    try {
      await this.ensureConnection();
      await this.connection!.flushAll();
    } catch (error) {
      console.warn('Redis flush failed:', error);
      throw error;
    }
  }

  async pubsub(channel: string, event: string, data: any): Promise<void> {
    try {
      await this.ensureConnection();
      const message = JSON.stringify({
        event,
        data,
      });

      await this.connection!.publish(channel, message);
    } catch (error) {
      console.warn('Redis pubsub failed:', error);
      throw error;
    }
  }

  private _serialize(value: any): string {
    return JSON.stringify(value);
  }

  private _unserialize(value: string): any {
    try {
      return JSON.parse(value);
    } catch {
      return value;
    }
  }

  async disconnect(): Promise<void> {
    if (this.connection && this.isConnected) {
      await this.connection.disconnect();
      this.isConnected = false;
      this.connection = null;
    }
  }

  isRedisConnected(): boolean {
    return this.isConnected && this.connection !== null;
  }
}

export default new Cache();
