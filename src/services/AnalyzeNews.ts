import { TelegramChannelListener } from './TelegramChannelListener';
import promptSync from 'prompt-sync';
import OpenAI from 'openai';
import { NewsItem, NewsAnalysisResult, NewsAnalysisResponse } from '../types';
import TelegramBot from 'node-telegram-bot-api';
import dayjs from 'dayjs';

export default class AnalyzeNews {
  private telegramService;
  private openai: OpenAI;
  private bot: TelegramBot;

  constructor() {
    this.telegramService = new TelegramChannelListener(
      Number(process.env.TELEGRAM_APP_API_ID!),
      process.env.TELEGRAM_APP_HASH!,
    );
    this.bot = new TelegramBot(process.env.TELEGRAM_BOT_ID!, { polling: true });
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY!,
    });
  }

  async init() {
    const prompt = promptSync();

    await this.telegramService.connect({
      phoneNumber: async () => prompt('📱 Enter phone number: '),
      phoneCode: async () => prompt('📩 Enter the code you received: '),
      onError: (err: any) => console.error(err),
    });

    this.setupCommands();
  }

  private setupCommands() {
    this.bot.onText(/\/news (\w+)/, async (msg, match) => {
      const chatId = msg.chat.id;
      const period = match?.[1];

      if (!period) {
        this.bot.sendMessage(chatId, 'Usage: /news 1d or /news 1w');
        return;
      }

      try {
        await this.handleNewsCommand(chatId, period);
      } catch (error) {
        this.bot.sendMessage(chatId, `Error: ${error}`);
      }
    });

    this.bot.onText(/\/pro (\w+)/, async (msg, match) => {
      const chatId = msg.chat.id;
      const period = match?.[1];

      if (!period) {
        this.bot.sendMessage(chatId, 'Usage: /pro 1d or /pro 1w for professional trading analysis');
        return;
      }

      try {
        await this.handleProAnalysisCommand(chatId, period);
      } catch (error) {
        this.bot.sendMessage(chatId, `Error: ${error}`);
      }
    });

    this.bot.onText(/\/start/, (msg) => {
      const chatId = msg.chat.id;
      this.bot.sendMessage(
        chatId,
        `Welcome to Professional Market Analysis Bot! 📊

Commands:
/news 1d - Daily market analysis
/news 1w - Weekly market analysis
/pro 1d - Professional trading insights (daily)
/pro 1w - Professional trading insights (weekly)

Get institutional-quality market analysis powered by AI! 🚀`,
      );
    });
  }

  private async handleNewsCommand(chatId: number, period: string, channelUsername = 'vnwallstreet') {
    try {
      this.bot.sendMessage(chatId, `Fetching messages...`);

      const { fromDate, toDate } = this.calculateDateRange(period);

      const channelMessages = await this.telegramService.getMessagesByDateRange(
        channelUsername,
        fromDate,
        toDate,
        500, // Fetch up to 500 messages
      );

      const newsMessages = channelMessages;

      if (newsMessages.length === 0) {
        this.bot.sendMessage(chatId, `No news found in ${channelUsername} for the last ${period}.`);
        return;
      }

      const newsItems: NewsItem[] = newsMessages.map((msg) => ({
        timestamp: msg.date.toISOString(),
        message: msg.message,
        source: 'telegram_channel',
      }));

      this.bot.sendMessage(chatId, `Analyzing ${newsItems.length} news items from the last ${period}...`);

      const result = await this.analyzeNews(newsItems);

      const message = this.formatAnalysisMessage(result.analysis, result.summary);
      this.bot.sendMessage(chatId, message, { parse_mode: 'HTML' });
    } catch (error) {
      console.error('Error in handleNewsCommand:', error);
      this.bot.sendMessage(chatId, `Error fetching messages from ${channelUsername}: ${error}`);
    }
  }

  private async handleProAnalysisCommand(chatId: number, period: string, channelUsername = 'vnwallstreet') {
    try {
      this.bot.sendMessage(chatId, `🔍 Fetching data for professional analysis...`);

      const { fromDate, toDate } = this.calculateDateRange(period);

      const channelMessages = await this.telegramService.getMessagesByDateRange(
        channelUsername,
        fromDate,
        toDate,
        500, // Fetch up to 500 messages
      );

      const newsMessages = channelMessages;

      if (newsMessages.length === 0) {
        this.bot.sendMessage(chatId, `No news found in ${channelUsername} for the last ${period}.`);
        return;
      }

      const newsItems: NewsItem[] = newsMessages.map((msg) => ({
        timestamp: msg.date.toISOString(),
        message: msg.message,
        source: 'telegram_channel',
      }));

      this.bot.sendMessage(
        chatId,
        `📊 Running professional analysis on ${newsItems.length} news items from the last ${period}...`,
      );

      const result = await this.analyzeNewsWithTradingInsights(newsItems);

      const message = this.formatProAnalysisMessage(result.analysis, result.summary);
      this.bot.sendMessage(chatId, message, { parse_mode: 'HTML' });
    } catch (error) {
      console.error('Error in handleProAnalysisCommand:', error);
      this.bot.sendMessage(chatId, `Error fetching professional analysis from ${channelUsername}: ${error}`);
    }
  }

  private formatProAnalysisMessage(analysis: any, summary: string): string {
    const timestamp = new Date().toLocaleString('vi-VN', {
      timeZone: 'Asia/Ho_Chi_Minh',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });

    return `
<b>🏦 INSTITUTIONAL MARKET ANALYSIS</b>
<i>🕐 ${timestamp} (GMT+7) | Professional Trading Insights</i>

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

<b>📈 EQUITIES OUTLOOK:</b>
${analysis.Equities}

<b>💱 FX STRATEGY:</b>
${analysis.FX}

<b>🛢️ COMMODITIES POSITIONING:</b>
${analysis.Commodities}

<b>₿ CRYPTO ALLOCATION:</b>
${analysis.Crypto}

<b>📊 RATES & DURATION:</b>
${analysis.Rates}

<b>🌍 GEOPOLITICAL RISK:</b>
${analysis.Geopolitics}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

<b>🎯 DOMINANT THEME:</b>
${analysis.KeyTheme}

<b>💼 PORTFOLIO STRATEGY & TRADING PLAN:</b>
${summary}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
<i>⚠️ Professional analysis for institutional use. Risk management essential.</i>
    `.trim();
  }

  private formatAnalysisMessage(analysis: any, summary: string): string {
    const timestamp = new Date().toLocaleString('vi-VN', {
      timeZone: 'Asia/Ho_Chi_Minh',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });

    return `
<b>📊 MARKET ANALYSIS REPORT</b>
<i>🕐 ${timestamp} (GMT+7)</i>

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

<b>🏛️ EQUITIES (Chứng khoán):</b>
${analysis.Equities}

<b>💱 FX (Ngoại hối):</b>
${analysis.FX}

<b>🛢️ COMMODITIES (Hàng hóa):</b>
${analysis.Commodities}

<b>₿ CRYPTO (Tiền điện tử):</b>
${analysis.Crypto}

<b>📈 RATES (Lãi suất/Trái phiếu):</b>
${analysis.Rates}

<b>🌍 GEOPOLITICS (Địa chính trị):</b>
${analysis.Geopolitics}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

<b>🎯 MARKET THEME:</b>
${analysis.KeyTheme}

<b>📝 EXECUTIVE SUMMARY & TRADING OUTLOOK:</b>
${summary}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
<i>⚠️ Disclaimer: For informational purposes only. Not financial advice.</i>
    `.trim();
  }

  private calculateDateRange(period: string): { fromDate: Date; toDate: Date } {
    const now = dayjs();
    let fromDate: dayjs.Dayjs;

    switch (period) {
      case '1d':
        fromDate = now.subtract(1, 'day');
        break;
      case '1w':
        fromDate = now.subtract(1, 'week');
        break;
      default:
        throw new Error('Invalid period. Use 1d or 1w.');
    }

    return {
      fromDate: fromDate.toDate(),
      toDate: now.toDate(),
    };
  }

  private optimizeNewsData(newsData: NewsItem[]): string {
    const uniqueNews = this.removeDuplicateNews(newsData);

    const groupedNews = this.groupAndSummarizeNews(uniqueNews);

    const optimizedNews = groupedNews.map((item, index) => ({
      id: index + 1,
      date: dayjs(item.timestamp).format('YYYY-MM-DD HH:mm'),
      content: this.extractImportantContent(item.message),
    }));

    optimizedNews.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    const maxItems = Math.min(40, optimizedNews.length); // Adaptive limit
    const limitedNews = optimizedNews.slice(0, maxItems);

    return JSON.stringify(limitedNews, null, 2);
  }

  private removeDuplicateNews(newsData: NewsItem[]): NewsItem[] {
    const seen = new Set<string>();
    const unique: NewsItem[] = [];

    for (const item of newsData) {
      const simplified = item.message
        .toLowerCase()
        .replace(/[^\w\s]/g, '')
        .replace(/\s+/g, ' ')
        .trim();

      const key = simplified.substring(0, 100);

      if (!seen.has(key)) {
        seen.add(key);
        unique.push(item);
      }
    }

    return unique;
  }

  private extractKeyContent(message: string): string {
    let cleaned = message
      .replace(/🔴/g, '')
      .replace(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/gu, '') // Remove other emojis
      .replace(/https?:\/\/[^\s]+/g, '[LINK]')
      .replace(/\s+/g, ' ')
      .trim();

    if (cleaned.length > 300) {
      cleaned = cleaned.substring(0, 300) + '...';
    }

    return cleaned;
  }

  private estimateTokens(text: string): number {
    return Math.ceil(text.length / 3);
  }

  private groupAndSummarizeNews(newsData: NewsItem[]): NewsItem[] {
    const groups: { [key: string]: NewsItem[] } = {};

    const topicKeywords = {
      monetary: [
        // Vietnamese terms
        'lãi suất',
        'ngân hàng',
        'chính sách tiền tệ',
        'sbv',
        'ngân hàng nhà nước',
        'tăng lãi suất',
        'giảm lãi suất',
        'tái cấp vốn',
        'dự trữ bắt buộc',
        'tỷ lệ dự trữ',
        'thanh khoản',
        'cung tiền',
        // International terms
        'fed',
        'interest rate',
        'monetary policy',
        'central bank',
        'rate hike',
        'rate cut',
      ],
      economic: [
        // Vietnamese terms
        'gdp',
        'tăng trưởng',
        'kinh tế',
        'lạm phát',
        'cpi',
        'pmi',
        'xuất khẩu',
        'nhập khẩu',
        'thương mại',
        'sản xuất công nghiệp',
        'bán lẻ',
        'tiêu dùng',
        'đầu tư',
        'fdi',
        'thất nghiệp',
        'việc làm',
        // International terms
        'inflation',
        'employment',
        'trade',
        'export',
        'import',
        'manufacturing',
        'retail',
      ],
      equity: [
        // Vietnamese terms
        'chứng khoán',
        'cổ phiếu',
        'vn-index',
        'hnx',
        'upcom',
        'thị trường chứng khoán',
        'niêm yết',
        'giao dịch',
        'khối lượng',
        'thanh khoản',
        'tăng điểm',
        'giảm điểm',
        'blue chip',
        // International terms
        'stock',
        'equity',
        'market',
        'index',
        'dow',
        'nasdaq',
        's&p',
        'trading',
        'shares',
      ],
      fx: [
        // Vietnamese terms
        'tỷ giá',
        'usd/vnd',
        'ngoại tệ',
        'đồng việt nam',
        'tỷ giá trung tâm',
        'biến động tỷ giá',
        'dự trữ ngoại hối',
        'can thiệp tỷ giá',
        'phá giá',
        'tăng giá',
        // International terms
        'dollar',
        'euro',
        'currency',
        'forex',
        'exchange rate',
        'usd',
        'eur',
        'jpy',
        'cny',
      ],
      commodity: [
        // Vietnamese terms
        'dầu thô',
        'xăng dầu',
        'vàng',
        'hàng hóa',
        'nông sản',
        'gạo',
        'cà phê',
        'cao su',
        'thép',
        'than',
        'điện',
        'gas',
        'giá xăng',
        'giá vàng',
        // International terms
        'oil',
        'crude',
        'gold',
        'commodity',
        'metal',
        'copper',
        'silver',
        'wheat',
        'corn',
      ],
      geopolitical: [
        // Vietnamese terms
        'chiến tranh',
        'xung đột',
        'thương chiến',
        'trừng phạt',
        'ngoại giao',
        'quan hệ quốc tế',
        'trung quốc',
        'mỹ',
        'nga',
        'ukraine',
        'biển đông',
        'asean',
        // International terms
        'war',
        'conflict',
        'trade war',
        'sanction',
        'china',
        'usa',
        'russia',
        'ukraine',
        'nato',
      ],
      crypto: [
        // Major cryptocurrencies
        'bitcoin',
        'btc',
        'ethereum',
        'eth',
        'crypto',
        'cryptocurrency',
        'tiền điện tử',
        'tiền mã hóa',
        // Vietnamese crypto terms
        'bitcoin',
        'ethereum',
        'tiền ảo',
        'tài sản số',
        'blockchain',
        'defi',
        'nft',
        // Exchanges and platforms
        'binance',
        'coinbase',
        'ftx',
        'kraken',
        'bitfinex',
        'huobi',
        'okx',
        // Major altcoins
        'altcoin',
        'bnb',
        'ada',
        'sol',
        'dot',
        'avax',
        'matic',
        'link',
        'uni',
        'xrp',
        'ltc',
        'bch',
        'etc',
        'xlm',
        'trx',
        'eos',
        'atom',
        'algo',
        // Stablecoins
        'usdt',
        'usdc',
        'busd',
        'dai',
        'stablecoin',
        'tether',
        // Crypto market terms
        'mining',
        'staking',
        'yield farming',
        'liquidity',
        'dex',
        'cex',
        'wallet',
        'cold storage',
        'hot wallet',
        'private key',
        'seed phrase',
        'smart contract',
        'gas fee',
        'transaction fee',
        'confirmation',
        'market cap',
        'volume',
        'dominance',
        'fear and greed',
        'rsi',
        'macd',
        'support',
        'resistance',
        'breakout',
        'dump',
        'pump',
        'whale',
        'hodl',
        'fomo',
        'fud',
        'ath',
        'atl',
        'bull market',
        'bear market',
        // Vietnamese crypto slang
        'lên sàn',
        'xuống sàn',
        'pump',
        'dump',
        'hold',
        'all in',
      ],
    };

    // Classify news into groups
    for (const item of newsData) {
      const content = item.message.toLowerCase();
      let assigned = false;

      for (const [topic, keywords] of Object.entries(topicKeywords)) {
        if (keywords.some((keyword) => content.includes(keyword))) {
          if (!groups[topic]) groups[topic] = [];
          groups[topic].push(item);
          assigned = true;
          break;
        }
      }

      if (!assigned) {
        if (!groups['other']) groups['other'] = [];
        groups['other'].push(item);
      }
    }

    const summarized: NewsItem[] = [];
    for (const [topic, items] of Object.entries(groups)) {
      const sorted = items.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      const topItems = sorted.slice(0, topic === 'other' ? 2 : 4);
      summarized.push(...topItems);
    }

    return summarized;
  }

  private extractImportantContent(message: string): string {
    const importantPatterns = [
      // Percentages
      /\d+\.?\d*%/g,
      // Vietnamese currency (VND)
      /\d+[\d,]*\.?\d*\s*(vnd|đồng|triệu|tỷ)/gi,
      // Dollar amounts
      /\$\d+[\d,]*\.?\d*/g,
      // Basis points
      /\d+\.?\d*\s*(basis points|bps|điểm cơ bản)/gi,
      // Dates
      /\d{1,2}\/\d{1,2}\/\d{4}/g, // DD/MM/YYYY
      /\d{4}-\d{2}-\d{2}/g, // YYYY-MM-DD
      // Vietnamese directional changes
      /(tăng|giảm|tăng lên|giảm xuống|lên|xuống)\s*\d+/gi,
      // English directional changes
      /(increase|decrease|rise|fall|up|down)\s+\d+/gi,
      // Vietnamese market terms with numbers
      /(vn-index|hnx-index)\s*\d+/gi,
      // Exchange rates
      /(usd\/vnd|tỷ giá)\s*\d+/gi,
      // Crypto prices and market cap
      /(btc|bitcoin|eth|ethereum)\s*\$?\d+[\d,]*\.?\d*/gi,
      // Crypto percentage changes
      /(btc|eth|bitcoin|ethereum|crypto)\s*(tăng|giảm|up|down)\s*\d+\.?\d*%/gi,
      // Market cap patterns
      /market cap\s*\$?\d+[\d,]*\.?\d*[bmk]?/gi,
    ];

    let extracted = message;
    const importantInfo: string[] = [];

    // Extract important numerical data
    for (const pattern of importantPatterns) {
      const matches = extracted.match(pattern);
      if (matches) {
        importantInfo.push(...matches);
      }
    }

    // Extract Vietnamese-specific important terms
    const vietnameseImportantTerms = [
      /ngân hàng nhà nước/gi,
      /chính phủ/gi,
      /bộ tài chính/gi,
      /thủ tướng/gi,
      /quốc hội/gi,
      /fed/gi,
      /ecb/gi,
    ];

    for (const pattern of vietnameseImportantTerms) {
      const matches = extracted.match(pattern);
      if (matches) {
        importantInfo.push(...matches);
      }
    }

    extracted = this.extractKeyContent(message);

    if (importantInfo.length > 0) {
      const uniqueInfo = [...new Set(importantInfo)].join(', ');
      extracted = `[Dữ liệu quan trọng: ${uniqueInfo}] ${extracted}`;
    }

    return extracted;
  }

  async analyzeNews(newsData: NewsItem[]): Promise<NewsAnalysisResult> {
    const optimizedData = this.optimizeNewsData(newsData);
    const estimatedTokens = this.estimateTokens(optimizedData);

    console.log(`📊 Optimization Results:`);
    console.log(`- Original news items: ${newsData.length}`);
    console.log(`- Optimized items: ${JSON.parse(optimizedData).length}`);
    console.log(`- Estimated tokens: ${estimatedTokens}`);

    const systemPrompt = `You are a senior macroeconomic and financial markets analyst with 15+ years of experience at top-tier investment banks (Goldman Sachs, JPMorgan, Morgan Stanley). You specialize in:

🎯 EXPERTISE AREAS:
- Vietnamese and Southeast Asian markets (VN-Index, HNX, UPCOM)
- Global macro trends and central bank policies (Fed, ECB, BOJ, SBV)
- FX markets (USD/VND, major pairs, emerging market currencies)
- Commodities (oil, gold, agricultural products, industrial metals)
- Cryptocurrency markets (BTC, ETH, DeFi, market structure)
- Geopolitical risk assessment and market impact analysis

📊 ANALYTICAL APPROACH:
- Use quantitative data and technical indicators when available
- Consider market sentiment, positioning, and flow dynamics
- Assess both immediate and medium-term market implications
- Provide actionable insights with confidence levels
- Reference historical precedents and market cycles

🎨 COMMUNICATION STYLE:
- Write like a Bloomberg/Reuters market analyst
- Use professional financial terminology
- Include specific price levels, percentages, and timeframes
- Provide clear risk/reward assessments
- Maintain objectivity while showing market conviction

Analyze news efficiently and provide concise, actionable insights in Vietnamese with the authority of a seasoned market professional.`;

    const userPrompt = `🔍 MARKET ANALYSIS REQUEST - Phân tích chuyên sâu dữ liệu tin tức tài chính:

📰 RAW DATA:
${optimizedData}

🎯 NHIỆM VỤ PHÂN TÍCH:

1️⃣ MARKET IMPACT ASSESSMENT:
- Phân loại tin tức theo asset class và đánh giá tác động định lượng
- Sử dụng scale: BULLISH (+2), POSITIVE (+1), NEUTRAL (0), NEGATIVE (-1), BEARISH (-2)
- Bao gồm confidence level (High/Medium/Low) cho mỗi đánh giá

2️⃣ CROSS-ASSET CORRELATION:
- Phân tích mối liên hệ giữa các thị trường
- Xác định trade opportunities và risk-off/risk-on flows

3️⃣ TIMING & CATALYSTS:
- Xác định timeline cho market moves (immediate/1-3 days/1-2 weeks)
- Highlight key upcoming events và potential catalysts

4️⃣ JSON OUTPUT FORMAT:
{
  "Equities": "📈 [IMPACT: +1/Medium] Phân tích chi tiết VN-Index, blue chips, sectors. Include price targets nếu có data.",
  "FX": "💱 [IMPACT: -1/High] Phân tích USD/VND, major pairs, central bank actions. Include technical levels.",
  "Commodities": "🛢️ [IMPACT: +2/High] Oil, gold, agricultural products. Include supply/demand factors.",
  "Crypto": "₿ [IMPACT: 0/Low] BTC, ETH, altcoins, DeFi trends. Include on-chain metrics nếu relevant.",
  "Rates": "📊 [IMPACT: +1/Medium] Bond yields, central bank policy, yield curve analysis.",
  "Geopolitics": "🌍 [IMPACT: -2/High] Regional tensions, trade relations, policy implications.",
  "KeyTheme": "🎯 Dominant market narrative và positioning recommendations"
}

5️⃣ EXECUTIVE SUMMARY:
Viết market outlook 3-4 câu với:
- Top 2 trading ideas với entry/exit levels
- Key risks to monitor
- Overall market bias (Risk-On/Risk-Off/Mixed)`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt },
        ],
        temperature: 0.7,
        max_tokens: 2000,
      });

      const content = response.choices[0]?.message?.content;

      if (!content) {
        throw new Error('No content received from OpenAI');
      }

      const jsonMatch = content.match(/\{[\s\S]*?\}/);
      if (!jsonMatch) {
        throw new Error('No JSON structure found in response');
      }

      const analysis: NewsAnalysisResponse = JSON.parse(jsonMatch[0]);

      const summaryMatch = content.split(jsonMatch[0])[1]?.trim();
      const summary = summaryMatch || 'No summary available';

      return {
        analysis,
        summary,
      };
    } catch (error) {
      throw new Error(`Failed to analyze news: ${error}`);
    }
  }

  /**
   * Enhanced market analysis with professional insights and trading recommendations
   */
  async analyzeNewsWithTradingInsights(newsData: NewsItem[]): Promise<NewsAnalysisResult> {
    const optimizedData = this.optimizeNewsData(newsData);
    const estimatedTokens = this.estimateTokens(optimizedData);

    console.log(`📊 Enhanced Analysis - Optimization Results:`);
    console.log(`- Original news items: ${newsData.length}`);
    console.log(`- Optimized items: ${JSON.parse(optimizedData).length}`);
    console.log(`- Estimated tokens: ${estimatedTokens}`);

    const systemPrompt = `You are a senior portfolio manager and market strategist at a top-tier hedge fund with $10B+ AUM. You have:

🎖️ CREDENTIALS:
- CFA Charter, 20+ years buy-side experience
- Former Goldman Sachs MD, ex-Bridgewater PM
- Specialized in Asian markets, macro trading, multi-asset strategies
- Track record: 15%+ annual returns, max drawdown <8%

🧠 ANALYTICAL FRAMEWORK:
- Apply modern portfolio theory and behavioral finance
- Use quantitative models: momentum, mean reversion, volatility clustering
- Consider market microstructure, liquidity, and positioning data
- Factor in central bank communications and policy divergence
- Assess tail risks and black swan scenarios

💼 INVESTMENT PHILOSOPHY:
- Risk-adjusted returns over absolute performance
- Diversification across time horizons and asset classes
- Contrarian when consensus is extreme, momentum when trends are strong
- Always consider correlation breakdowns during stress periods

Provide institutional-quality analysis with specific position sizing, risk management, and portfolio allocation recommendations.`;

    const userPrompt = `🎯 INSTITUTIONAL MARKET ANALYSIS REQUEST

📊 MARKET DATA FEED:
${optimizedData}

🔍 REQUIRED DELIVERABLES:

1️⃣ MARKET IMPACT MATRIX:
- Quantify impact on each asset class (-3 to +3 scale)
- Include conviction level (1-5) and time horizon
- Identify correlation shifts and regime changes

2️⃣ TRADING RECOMMENDATIONS:
- Specific entry/exit levels with stop losses
- Position sizing (% of portfolio allocation)
- Risk/reward ratios and expected holding periods

3️⃣ PORTFOLIO IMPLICATIONS:
- Sector rotation opportunities
- Currency hedging recommendations
- Volatility positioning (long/short vol)

4️⃣ JSON RESPONSE FORMAT:
{
  "Equities": "📈 [IMPACT: +2/5, 1-2W] VN-Index target 1,250-1,280. Overweight banks, underweight real estate. Allocation: 35% (+5%)",
  "FX": "💱 [IMPACT: -1/4, 3-5D] USD/VND range 24,100-24,300. Short USD on Fed pause. Hedge 50% FX exposure.",
  "Commodities": "🛢️ [IMPACT: +3/5, 1-3D] Oil breakout above $85. Long WTI, target $90. Position size: 8% portfolio.",
  "Crypto": "₿ [IMPACT: 0/2, 1W] BTC consolidation 26k-28k. Wait for breakout. Max allocation: 3%.",
  "Rates": "📊 [IMPACT: +1/3, 2W] Yield curve steepening. Long 10Y, short 2Y. Duration: +0.5 years.",
  "Geopolitics": "🌍 [IMPACT: -2/4, Ongoing] Elevated tail risk. Increase cash to 15%, buy VIX calls.",
  "KeyTheme": "🎯 Central bank divergence creating cross-asset opportunities. Focus on carry trades and volatility arbitrage."
}

5️⃣ EXECUTIVE SUMMARY:
- Top 3 highest conviction trades with specific entry points
- Key risk factors and hedging strategies
- Overall portfolio positioning (Risk-On/Risk-Off/Balanced)
- Next week's key events to monitor`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt },
        ],
        temperature: 0.3, // Lower temperature for more consistent professional analysis
        max_tokens: 3000,
      });

      const content = response.choices[0]?.message?.content;

      if (!content) {
        throw new Error('No content received from OpenAI');
      }

      const jsonMatch = content.match(/\{[\s\S]*?\}/);
      if (!jsonMatch) {
        throw new Error('No JSON structure found in response');
      }

      const analysis: NewsAnalysisResponse = JSON.parse(jsonMatch[0]);

      const summaryMatch = content.split(jsonMatch[0])[1]?.trim();
      const summary = summaryMatch || 'No summary available';

      return {
        analysis,
        summary,
      };
    } catch (error) {
      throw new Error(`Failed to analyze news with trading insights: ${error}`);
    }
  }
}
